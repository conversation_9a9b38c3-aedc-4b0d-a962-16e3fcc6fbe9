package mnms

import (
	"encoding/json"
	"fmt"
	"io"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/gosnmp/gosnmp"
	"github.com/qeof/q"
)

// DeviceIdentifierMapping stores the relationship between IP, MAC, and chassis ID
type DeviceIdentifierMapping struct {
	IPAddress string
	MAC       string
	ChassisID string
}

// DeviceStateTracker tracks the online/offline state of devices
type DeviceStateTracker struct {
	mutex        sync.RWMutex
	deviceStates map[string]bool                     // deviceID -> isOnline
	ipMappings   map[string]*DeviceIdentifierMapping // IP -> mapping
}

var stateTracker = &DeviceStateTracker{
	deviceStates: make(map[string]bool),
	ipMappings:   make(map[string]*DeviceIdentifierMapping),
}

// getStateTrackerStats returns statistics about the current state tracker
func getStateTrackerStats() (total, online, offline int) {
	stateTracker.mutex.RLock()
	defer stateTracker.mutex.RUnlock()

	total = len(stateTracker.deviceStates)
	for _, isOnline := range stateTracker.deviceStates {
		if isOnline {
			online++
		} else {
			offline++
		}
	}
	return total, online, offline
}

func StartPolling(nmsName string, pollingInterval int) {
	for {
		err := PollNMSDevices(nmsName)
		if err != nil {
			q.Q("Error polling NMS devices:", err)
		}
		time.Sleep(time.Second * time.Duration(pollingInterval))
	}
}

func PollNMSDevices(nmsName string) error {
	q.Q("Polling NMS devices for", nmsName)

	// Step 1: Get device information from root MNMS
	devices, err := getDevicesFromRoot()
	if err != nil {
		q.Q("Error getting devices from root:", err)
		return err
	}

	// Step 2: Filter devices with matching NMS name
	filteredDevices := filterDevicesByNMS(devices, nmsName)
	q.Q("Found", len(filteredDevices), "devices for NMS:", nmsName)

	// Step 3: Check device connectivity and detect state changes
	checkDeviceConnectivityAndNotify(filteredDevices)

	// Step 4: Discover LLDP topology for non-agent devices
	discoverLLDPTopology(filteredDevices, devices)

	// Step 5: Log state tracker statistics
	total, online, offline := getStateTrackerStats()
	q.Q("State tracker stats - Total:", total, "Online:", online, "Offline:", offline)

	return nil
}

// getDevicesFromRoot retrieves device information from the root MNMS server
func getDevicesFromRoot() (map[string]DevInfo, error) {
	url := QC.RootURL + "/api/v1/devices"

	resp, err := GetWithToken(url, QC.AdminToken)
	if err != nil {
		return nil, fmt.Errorf("failed to get devices: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("HTTP error %d: %s", resp.StatusCode, string(body))
	}

	var devices map[string]DevInfo
	err = json.NewDecoder(resp.Body).Decode(&devices)
	if err != nil {
		return nil, fmt.Errorf("failed to decode devices: %w", err)
	}

	return devices, nil
}

// filterDevicesByNMS filters devices that belong to the specified NMS
func filterDevicesByNMS(devices map[string]DevInfo, nmsName string) []DevInfo {
	var filtered []DevInfo

	for _, device := range devices {
		// Check if device was scanned by this NMS
		if device.ScannedBy == nmsName {
			filtered = append(filtered, device)
		}
	}

	return filtered
}

// checkDeviceConnectivityAndNotify checks device connectivity and sends notifications only on state changes
func checkDeviceConnectivityAndNotify(devices []DevInfo) {
	var newlyOfflineDevices []string
	var newlyOnlineDevices []string

	for _, device := range devices {
		currentOnlineStatus := false

		if device.IPAddress == "" {
			q.Q("Device", device.Mac, "has no IP address, considering offline")
			currentOnlineStatus = false
		} else {
			// Step 1: Try SNMP sysDescr (*******.*******.0)
			if checkSNMPConnectivity(device.IPAddress) {
				q.Q("Device", device.Mac, "is online via SNMP")
				currentOnlineStatus = true

				// Get chassis ID and update mapping when SNMP is working
				chassisID := getDeviceChassisID(device.IPAddress)
				updateDeviceMapping(device.IPAddress, device.Mac, chassisID)
			} else {
				// Step 2: Try ping if SNMP fails
				if CheckDeviceExisted(device.IPAddress) {
					q.Q("Device", device.Mac, "is online via ping")
					currentOnlineStatus = true
				} else {
					q.Q("Device", device.Mac, "is offline")
					currentOnlineStatus = false
				}
			}
		}

		// Process state changes for this IP (handles both MAC and chassis ID)
		processDeviceStateChanges(device.IPAddress, device.Mac, currentOnlineStatus, &newlyOnlineDevices, &newlyOfflineDevices)
	}

	// Send notifications for state changes
	q.Q("Notification summary - Newly offline:", newlyOfflineDevices, "Newly online:", newlyOnlineDevices)
	if len(newlyOfflineDevices) > 0 {
		sendDeviceStateChangeResults("offline", newlyOfflineDevices)
	}
	if len(newlyOnlineDevices) > 0 {
		sendDeviceStateChangeResults("online", newlyOnlineDevices)
	}

	if len(newlyOfflineDevices) == 0 && len(newlyOnlineDevices) == 0 {
		q.Q("No device state changes detected")
	}
}

// checkSNMPConnectivity checks if SNMP sysDescr OID is reachable
func checkSNMPConnectivity(ipAddress string) bool {
	// SNMP OID for sysDescr
	oids := []string{"*******.*******.0"}

	result, err := SnmpGet(ipAddress, oids)
	if err != nil {
		q.Q("SNMP check failed for", ipAddress, ":", err)
		return false
	}

	if result != nil && len(result.Variables) > 0 {
		q.Q("SNMP sysDescr for", ipAddress, ":", result.Variables[0].Value)
		return true
	}

	return false
}

// getDeviceChassisID retrieves the LLDP chassis ID from a device
func getDeviceChassisID(ipAddress string) string {
	// LLDP OID for local chassis ID
	const lldpLocChassisId = "1.0.8802.*******.3.2.0"

	result, err := SnmpGet(ipAddress, []string{lldpLocChassisId})
	if err != nil {
		q.Q("Failed to get chassis ID for", ipAddress, ":", err)
		return ""
	}

	if result != nil && len(result.Variables) > 0 {
		chassisID := formatLLDPID(result.Variables[0])
		q.Q("Chassis ID for", ipAddress, ":", chassisID)
		return chassisID
	}

	return ""
}

// updateDeviceState updates the device state tracker for a specific device ID
func updateDeviceState(deviceID string, isOnline bool, deviceType string) {
	stateTracker.mutex.Lock()
	defer stateTracker.mutex.Unlock()

	previousState, exists := stateTracker.deviceStates[deviceID]

	// Update the state
	stateTracker.deviceStates[deviceID] = isOnline

	// Log state changes
	if !exists {
		if isOnline {
			q.Q("Device", deviceType, deviceID, "discovered online (first time)")
		} else {
			q.Q("Device", deviceType, deviceID, "discovered offline (first time)")
		}
	} else if previousState != isOnline {
		if isOnline {
			q.Q("Device", deviceType, deviceID, "state changed: offline -> online")
		} else {
			q.Q("Device", deviceType, deviceID, "state changed: online -> offline")
		}
	}
}

// updateDeviceStateAndCheckChange updates device state and returns true if state changed (not first discovery)
func updateDeviceStateAndCheckChange(deviceID string, isOnline bool, deviceType string) bool {
	stateTracker.mutex.Lock()
	defer stateTracker.mutex.Unlock()

	previousState, exists := stateTracker.deviceStates[deviceID]

	// Update the state
	stateTracker.deviceStates[deviceID] = isOnline

	// Check if this is a state change that should trigger notifications
	stateChanged := false

	if !exists {
		// First time seeing this device - don't send syslog notification
		if isOnline {
			q.Q("Device", deviceType, deviceID, "discovered online (first time)")
		} else {
			q.Q("Device", deviceType, deviceID, "discovered offline (first time)")
		}
		// Don't trigger syslog for first-time discoveries
		stateChanged = false
	} else if previousState != isOnline {
		// State actually changed - send syslog notification
		stateChanged = true
		if isOnline {
			q.Q("Device", deviceType, deviceID, "state changed: offline -> online")
		} else {
			q.Q("Device", deviceType, deviceID, "state changed: online -> offline")
		}
	}

	return stateChanged
}

// updateDeviceMapping updates the IP to MAC/chassis ID mapping when SNMP is working
func updateDeviceMapping(ipAddress, mac, chassisID string) {
	stateTracker.mutex.Lock()
	defer stateTracker.mutex.Unlock()

	mapping := &DeviceIdentifierMapping{
		IPAddress: ipAddress,
		MAC:       mac,
		ChassisID: chassisID,
	}

	stateTracker.ipMappings[ipAddress] = mapping

	if chassisID != "" && chassisID != mac {
		q.Q("Updated mapping for IP", ipAddress, "- MAC:", mac, "Chassis:", chassisID)
	} else {
		q.Q("Updated mapping for IP", ipAddress, "- MAC:", mac, "(chassis same as MAC)")
	}
}

// processDeviceStateChanges processes state changes for both MAC and chassis ID based on IP mapping
func processDeviceStateChanges(ipAddress, mac string, isOnline bool, newlyOnlineDevices, newlyOfflineDevices *[]string) {
	stateTracker.mutex.RLock()
	mapping, hasMappingData := stateTracker.ipMappings[ipAddress]
	stateTracker.mutex.RUnlock()

	var devicesToCheck []string

	if hasMappingData && mapping.ChassisID != "" && mapping.ChassisID != mapping.MAC {
		// Device has different MAC and chassis ID - check both
		devicesToCheck = []string{mapping.MAC, mapping.ChassisID}
		q.Q("IP", ipAddress, "has different MAC/chassis - checking both:", mapping.MAC, "and", mapping.ChassisID)
	} else {
		// Device has same MAC and chassis ID or no chassis ID - check only MAC
		devicesToCheck = []string{mac}
		q.Q("IP", ipAddress, "checking only MAC:", mac)
	}

	// Process state changes for all relevant device IDs
	for _, deviceID := range devicesToCheck {
		deviceType := "MAC"
		if hasMappingData && deviceID == mapping.ChassisID && deviceID != mapping.MAC {
			deviceType = "chassis"
		}

		stateChanged := updateDeviceStateAndCheckChange(deviceID, isOnline, deviceType)
		q.Q("Device", deviceType, deviceID, "state changed:", stateChanged, "status:", isOnline)

		if stateChanged {
			if isOnline {
				*newlyOnlineDevices = append(*newlyOnlineDevices, deviceID)
				q.Q("Added", deviceType, deviceID, "to newly online devices")
			} else {
				*newlyOfflineDevices = append(*newlyOfflineDevices, deviceID)
				q.Q("Added", deviceType, deviceID, "to newly offline devices")
			}
		}
	}
}

// sendDeviceStateChangeResults sends device state change notifications via syslog
func sendDeviceStateChangeResults(state string, deviceMACs []string) {
	// Create comma-separated list of MAC addresses
	macList := strings.Join(deviceMACs, ",")

	// Create syslog message
	message := fmt.Sprintf("Devices %s: %s", state, macList)

	// Send syslog with tag "devicePolling" and type info
	err := SendSyslog(LOG_ALERT, "devicePolling", message)
	if err != nil {
		q.Q("Error sending device state change syslog:", err)
	} else {
		q.Q("Sent device state change syslog:", message)
	}
}

// discoverLLDPTopology discovers network topology using LLDP for non-agent devices
func discoverLLDPTopology(devices []DevInfo, allDevices map[string]DevInfo) {
	q.Q("Starting LLDP topology discovery for", len(devices), "devices")

	var discoveredTopologies []*Topology

	for _, device := range devices {
		// Only discover topology for devices that are not scanned by agents
		if device.Scanproto == "agent" {
			q.Q("Skipping LLDP discovery for agent device:", device.Mac)
			continue
		}

		// Only discover topology for online devices with IP addresses
		if device.IPAddress == "" {
			q.Q("Skipping LLDP discovery for device with no IP:", device.Mac)
			continue
		}

		// Check if device is online in our state tracker
		stateTracker.mutex.RLock()
		isOnline, exists := stateTracker.deviceStates[device.Mac]
		stateTracker.mutex.RUnlock()

		if !exists || !isOnline {
			q.Q("Skipping LLDP discovery for offline device:", device.Mac)
			continue
		}

		q.Q("Discovering LLDP topology for device:", device.Mac, "IP:", device.IPAddress)

		// Get LLDP topology data from the device
		topologyData, err := getDeviceLLDPTopology(device, "public")
		if err != nil {
			q.Q("Failed to get LLDP topology for", device.Mac, ":", err)
			continue
		}

		if topologyData != nil {
			discoveredTopologies = append(discoveredTopologies, topologyData)
			q.Q("Successfully discovered LLDP topology for", device.Mac, "with", len(topologyData.LinkData), "links")
		}
	}

	// Publish discovered topologies
	if len(discoveredTopologies) > 0 {
		publishLLDPTopologies(discoveredTopologies)
		q.Q("Published", len(discoveredTopologies), "LLDP topologies")
	} else {
		q.Q("No LLDP topologies discovered")
	}
}

// formatLLDPID formats LLDP ID from SNMP PDU based on the working reference
func formatLLDPID(pdu gosnmp.SnmpPDU) string {
	switch v := pdu.Value.(type) {
	case []byte:
		if len(v) == 6 {
			// This looks like a MAC address
			mac := make([]string, 6)
			for i, b := range v {
				mac[i] = fmt.Sprintf("%02X", b)
			}
			return strings.Join(mac, "-")
		}
		return string(v)
	case int:
		return fmt.Sprintf("%d", v)
	case uint:
		return fmt.Sprintf("%d", v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// publishLLDPTopologies publishes discovered LLDP topologies to the root server
func publishLLDPTopologies(topologies []*Topology) {
	for _, topology := range topologies {
		// Convert DeviceData to Topology format
		links := make([]Link, len(topology.LinkData))
		for i, linkData := range topology.LinkData {
			links[i] = Link{
				Source:      linkData.Source,
				Target:      linkData.Target,
				SourcePort:  linkData.SourcePort,
				TargetPort:  linkData.TargetPort,
				Edge:        linkData.Edge,
				BlockedPort: linkData.BlockedPort,
				LinkType:    linkData.LinkType,
			}
		}

		topoData := Topology{
			Id:          topology.Id,
			IpAddress:   topology.IpAddress,
			ModelName:   topology.ModelName,
			Services:    topology.Services,
			TopoType:    topology.TopoType,
			LastUpdated: int(topology.LastUpdated),
			LinkData:    links,
		}

		// Publish topology to root server
		err := PublishTopology(topoData)
		if err != nil {
			q.Q("Failed to publish LLDP topology for", topology.Id, ":", err)
		} else {
			q.Q("Successfully published LLDP topology for", topology.Id, "with", len(links), "links")
		}
	}
}

func parseLLDPIndex(oid, baseOid string) string {
	// Remove the leading dot if present
	cleanOid := strings.TrimPrefix(oid, ".")
	cleanBaseOid := strings.TrimPrefix(baseOid, ".")

	// Remove the base OID and the following dot
	suffix := strings.TrimPrefix(cleanOid, cleanBaseOid)
	suffix = strings.TrimPrefix(suffix, ".")
	return suffix
}

// normalizeCiscoInterfaceName normalizes Cisco interface names for consistency
// Converts short forms like "Fa0/1" to long forms like "FastEthernet0/1"
func normalizeInterfaceName(interfaceName string) string {
	// Handle various Cisco short form interface names
	normalized := interfaceName

	// FastEthernet variations
	if strings.HasPrefix(interfaceName, "Fa") && !strings.HasPrefix(interfaceName, "FastEthernet") {
		normalized = strings.Replace(interfaceName, "Fa", "FastEthernet", 1)
	}

	// GigabitEthernet variations
	if strings.HasPrefix(interfaceName, "Gi") && !strings.HasPrefix(interfaceName, "GigabitEthernet") {
		normalized = strings.Replace(interfaceName, "Gi", "GigabitEthernet", 1)
	}

	// TenGigabitEthernet variations
	if strings.HasPrefix(interfaceName, "Te") && !strings.HasPrefix(interfaceName, "TenGigabitEthernet") {
		normalized = strings.Replace(interfaceName, "Te", "TenGigabitEthernet", 1)
	}

	// TwentyFiveGigE variations
	if strings.HasPrefix(interfaceName, "Twe") && !strings.HasPrefix(interfaceName, "TwentyFiveGigE") {
		normalized = strings.Replace(interfaceName, "Twe", "TwentyFiveGigE", 1)
	}

	// FortyGigabitEthernet variations
	if strings.HasPrefix(interfaceName, "Fo") && !strings.HasPrefix(interfaceName, "FortyGigabitEthernet") {
		normalized = strings.Replace(interfaceName, "Fo", "FortyGigabitEthernet", 1)
	}

	// HundredGigE variations
	if strings.HasPrefix(interfaceName, "Hu") && !strings.HasPrefix(interfaceName, "HundredGigE") {
		normalized = strings.Replace(interfaceName, "Hu", "HundredGigE", 1)
	}

	// Ethernet variations (generic)
	if strings.HasPrefix(interfaceName, "Et") && !strings.HasPrefix(interfaceName, "Ethernet") &&
		!strings.HasPrefix(interfaceName, "FastEthernet") && !strings.HasPrefix(interfaceName, "GigabitEthernet") {
		normalized = strings.Replace(interfaceName, "Et", "Ethernet", 1)
	}

	// Serial interface variations
	if strings.HasPrefix(interfaceName, "Se") && !strings.HasPrefix(interfaceName, "Serial") {
		normalized = strings.Replace(interfaceName, "Se", "Serial", 1)
	}

	// Loopback variations
	if strings.HasPrefix(interfaceName, "Lo") && !strings.HasPrefix(interfaceName, "Loopback") {
		normalized = strings.Replace(interfaceName, "Lo", "Loopback", 1)
	}

	// Tunnel variations
	if strings.HasPrefix(interfaceName, "Tu") && !strings.HasPrefix(interfaceName, "Tunnel") {
		normalized = strings.Replace(interfaceName, "Tu", "Tunnel", 1)
	}

	// VLAN variations
	if strings.HasPrefix(interfaceName, "Vl") && !strings.HasPrefix(interfaceName, "Vlan") {
		normalized = strings.Replace(interfaceName, "Vl", "Vlan", 1)
	}

	// Port-channel variations - be more specific to avoid matching "Port1"
	if strings.HasPrefix(interfaceName, "Po") && !strings.HasPrefix(interfaceName, "Port-channel") && !strings.HasPrefix(interfaceName, "Port") {
		normalized = strings.Replace(interfaceName, "Po", "Port-channel", 1)
	}

	// Handle "Port1" pattern - convert to "Port-1"
	if strings.HasPrefix(interfaceName, "Port") && len(interfaceName) > 4 {
		// Check if it's "Port" followed by digits
		portNum := interfaceName[4:]
		if isNumeric(portNum) {
			normalized = "Port-" + portNum
		}
	}

	return normalized
}

// isNumeric checks if a string contains only digits
func isNumeric(s string) bool {
	for _, char := range s {
		if char < '0' || char > '9' {
			return false
		}
	}
	return len(s) > 0
}

// getDeviceLLDPTopology retrieves LLDP topology data from a device
func getDeviceLLDPTopology(devData DevInfo, community string) (*Topology, error) {
	ip := devData.IPAddress

	// LLDP OIDs
	const (
		sysDescr         = "*******.*******.0"
		lldpLocChassisId = "1.0.8802.*******.3.2.0"
		lldpLocPortDesc  = "1.0.8802.*******.*******" // Local port description
		lldpRemChassisId = "1.0.8802.*******.*******"
		lldpRemPortId    = "1.0.8802.*******.*******"
		ifDescr          = "*******.*******.1.2" // Interface description table
	)

	q.Q("Starting LLDP discovery for device:", ip)

	// Step 1: Get local chassis ID and model
	localInfo, err := SnmpGet(ip, []string{lldpLocChassisId, sysDescr})
	if err != nil {
		return nil, fmt.Errorf("failed to get local device info: %w", err)
	}
	if len(localInfo.Variables) < 2 {
		return nil, fmt.Errorf("insufficient local device info")
	}

	localChassisID := formatLLDPID(localInfo.Variables[0])
	modelName := formatLLDPID(localInfo.Variables[1])

	// Truncate model name to 20 characters
	if len(modelName) > 20 {
		modelName = modelName[:20]
	}

	q.Q("Local device - Chassis:", localChassisID, "Model:", modelName)

	// Step 2: Build interface index to name mapping using standard interface table
	interfaceMap := make(map[string]string)
	ifResults, err := SnmpWalk(ip, ifDescr)
	if err == nil {
		for _, pdu := range ifResults {
			oidParts := strings.Split(pdu.Name, ".")
			ifIndex := oidParts[len(oidParts)-1]
			ifName := formatLLDPID(pdu)
			if ifName != "" {
				interfaceMap[ifIndex] = normalizeInterfaceName(ifName)
			}
		}
	}
	q.Q("Built interface map with", len(interfaceMap), "entries")

	// Step 3: Get LLDP local port descriptions and map to interface indexes
	localPortMap := make(map[string]string) // LLDP port index -> interface name
	portDescResults, err := SnmpWalk(ip, lldpLocPortDesc)
	if err == nil {
		for _, pdu := range portDescResults {
			oidParts := strings.Split(pdu.Name, ".")
			lldpPortIndex := oidParts[len(oidParts)-1]
			portDesc := formatLLDPID(pdu)

			if portDesc != "" {
				// Normalize the port description
				normalizedPort := normalizeInterfaceName(portDesc)
				localPortMap[lldpPortIndex] = normalizedPort
			} else {
				// If no description, try to match with interface table by index
				if ifName, exists := interfaceMap[lldpPortIndex]; exists {
					localPortMap[lldpPortIndex] = ifName
				} else {
					// Last resort: create generic port name
					genericPort := "Port-" + lldpPortIndex
					localPortMap[lldpPortIndex] = genericPort
				}
			}
		}
	} else {
		q.Q("Failed to get LLDP port descriptions:", err)
	}

	// Step 4: Get LLDP neighbor information
	remoteNeighbors := make(map[string]map[string]string) // index -> {chassis, port}

	// Get remote chassis IDs
	chassisResults, err := SnmpWalk(ip, lldpRemChassisId)
	if err != nil {
		return nil, fmt.Errorf("failed to get remote chassis IDs: %w", err)
	}

	for _, pdu := range chassisResults {
		index := parseLLDPIndex(pdu.Name, lldpRemChassisId)
		chassisID := formatLLDPID(pdu)
		if remoteNeighbors[index] == nil {
			remoteNeighbors[index] = make(map[string]string)
		}
		remoteNeighbors[index]["chassis"] = chassisID
	}

	// Get remote port IDs
	portResults, err := SnmpWalk(ip, lldpRemPortId)
	if err != nil {
		return nil, fmt.Errorf("failed to get remote port IDs: %w", err)
	}

	for _, pdu := range portResults {
		index := parseLLDPIndex(pdu.Name, lldpRemPortId)
		portID := formatLLDPID(pdu)
		if remoteNeighbors[index] == nil {
			remoteNeighbors[index] = make(map[string]string)
		}
		remoteNeighbors[index]["port"] = normalizeInterfaceName(portID)
	}

	// Step 5: Create topology links
	var links []Link
	for index, neighbor := range remoteNeighbors {
		remoteChassis := neighbor["chassis"]
		remotePort := neighbor["port"]

		if remoteChassis == "" || remotePort == "" {
			q.Q("Incomplete neighbor data for index", index)
			continue
		}

		// Extract local port index from LLDP index
		indexParts := strings.Split(index, ".")
		if len(indexParts) < 2 {
			q.Q("Invalid LLDP index format:", index)
			continue
		}

		localPortIndex := indexParts[1]
		sourcePort := localPortMap[localPortIndex]

		if sourcePort == "" {
			sourcePort = "Port-" + localPortIndex
		}

		// Create link
		macs := []string{localChassisID, remoteChassis}
		sort.Strings(macs)
		edgeID := strings.Join(macs, "_")

		link := Link{
			Source:      localChassisID,
			Target:      remoteChassis,
			SourcePort:  sourcePort,
			TargetPort:  remotePort,
			Edge:        edgeID,
			BlockedPort: "false",
			LinkType:    "snmp",
		}

		links = append(links, link)
	}

	// Step 6: Return topology data
	topology := &Topology{
		Id:          localChassisID,
		IpAddress:   ip,
		ModelName:   modelName,
		Services:    devData.ScannedBy,
		LastUpdated: int(time.Now().Unix()),
		LinkData:    links,
		TopoType:    "snmp",
	}

	q.Q("Completed LLDP discovery for", ip, "- found", len(links), "links")
	return topology, nil
}
