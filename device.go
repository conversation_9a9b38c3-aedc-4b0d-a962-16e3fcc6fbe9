package mnms

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/qeof/q"
)

// use in furutres #issue,1656
type IPAddress struct {
	IPAddress string `json:"ipaddress,omitempty"`
	Mac       string `json:"mac,omitempty"`
	Netmask   string `json:"netmask,omitempty"`
	Gateway   string `json:"gateway,omitempty"`
}

type DevInfo struct {
	Mac            string          `json:"mac,omitempty"`
	MacExtra       []string        `json:"macExtra,omitempty"`    // use in furutres #issue,1656
	IPAddrExtra    []IPAddress     `json:"ipAddrExtra,omitempty"` // use in furutres #issue,1656
	ModelName      string          `json:"modelname,omitempty"`
	Timestamp      string          `json:"timestamp,omitempty"`
	Scanproto      string          `json:"scanproto,omitempty"`
	IPAddress      string          `json:"ipaddress,omitempty"`
	Netmask        string          `json:"netmask,omitempty"`
	Gateway        string          `json:"gateway,omitempty"`
	Hostname       string          `json:"hostname,omitempty"`
	Kernel         string          `json:"kernel,omitempty"`
	Ap             string          `json:"ap,omitempty"`
	ScannedBy      string          `json:"scannedby,omitempty"`
	ArpMissed      int             `json:"arpmissed,omitempty"`
	Lock           bool            `json:"lock,omitempty"`
	ReadCommunity  string          `json:"readcommunity,omitempty"`
	WriteCommunity string          `json:"writecommunity,omitempty"`
	IsDHCP         bool            `json:"isdhcp,omitempty"`
	IsOnline       bool            `json:"isonline"`
	TopologyProto  string          `json:"topologyproto,omitempty"`
	SvcDiscoVia    string          `json:"svcdiscovia,omitempty"`
	Capabilities   map[string]bool `json:"capabilities,omitempty"`
	DeviceErrors   []string        `json:"device_errors,omitempty"`
	UserName       string          `json:"username,omitempty"`
	PassWord       string          `json:"password,omitempty"`
	TunneledUrl    string          `json:"tunneled_url,omitempty"`
	SnmpSupported  string          `json:"snmpSupported,omitempty"`
	SnmpEnabled    string          `json:"snmpEnabled,omitempty"`
	GwdModelName   string          `json:"gwdModelName,omitempty"`
	TrapSetting    []TrapSetting   `json:"trapSetting,omitempty"`
	SyslogSetting  SyslogSetting   `json:"syslogSetting,omitempty"`
	MotorInfo      MotorInfo       `json:"motorinfo,omitempty"`
	Type           string          `json:"type,omitempty"`
	GpsInfo        GpsInfo         `json:"gpsInfo,omitempty"`
	Supported      []string        `json:"supported,omitempty"`
	AgentVersion   string          `json:"agentVersion,omitempty"`
	Tag            string          `json:"tagged,omitempty"`
	Group          string          `json:"group,omitempty"`
	Status         string          `json:"status,omitempty"`
}

// editableItem which paramter could be edited
type editableItem struct {
	modelName string
	netmask   string
	hostname  string
}

func (d *DevInfo) Edit(item editableItem) {
	d.ModelName = item.modelName
	d.Netmask = item.netmask
	d.Hostname = item.hostname
}

var specialMac = "11-22-33-44-55-66"

var lastTimestamp string

func init() {
	QC.DevData = make(map[string]DevInfo)
	lastTimestamp = strconv.FormatInt(time.Now().Unix(), 10)
}

// InsertCommunities inserts communities into device list
func InsertCommunities(mac, read, write string) error {
	devinfo, err := FindDev(mac)
	if err != nil || devinfo == nil {
		return err
	}
	devinfo.ReadCommunity = read
	devinfo.WriteCommunity = write
	InsertAndPublishDevice(*devinfo)
	return nil
}

// InsertAgentDevices inserts agent into device list
func InsertAgentDevices(devinfo DevInfo) {
	if QC.RootURL != "" {
		if _, ok := QC.Clients[devinfo.ScannedBy]; !ok {
			devinfo.ScannedBy = QC.Name
		}
		devinfo.Timestamp = strconv.FormatInt(time.Now().Unix(), 10)
		devinfo.IsOnline = true
		devinfo.TopologyProto = "agent"
		devinfo.Capabilities = *AddCapabilities(devinfo)
		QC.DevMutex.Lock()
		dev, ok := QC.DevData[devinfo.Mac]
		QC.DevMutex.Unlock()
		if ok {
			devinfo.UserName = dev.UserName
			devinfo.PassWord = dev.PassWord
			if len(devinfo.DeviceErrors) == 0 {
				devinfo.DeviceErrors = dev.DeviceErrors
			}
			if len(devinfo.ReadCommunity) == 0 {
				devinfo.ReadCommunity = dev.ReadCommunity
			}
			if len(devinfo.WriteCommunity) == 0 {
				devinfo.WriteCommunity = dev.WriteCommunity
			}
			if len(devinfo.GwdModelName) == 0 {
				devinfo.GwdModelName = dev.GwdModelName
			}
		}
		if len(devinfo.AgentVersion) == 0 {
			devinfo.AgentVersion = "v1.0.0"
		}
		if len(devinfo.Supported) == 0 {
			devinfo.Supported = []string{"reboot", "beep", "snmp", "snmpTrap", "syslog", "network", "firmware"}
		}
	}
	InsertAndPublishDevice(devinfo)
}

func InsertModel(model GwdModelInfo, proto string) {
	var deviceDesc DevInfo
	devinfo, err := FindDev(model.MACAddress)
	if err == nil && devinfo != nil {
		deviceDesc = *devinfo
	}
	// new device give default valuse
	// discovered device model will be entered into device list
	deviceDesc.Mac = model.MACAddress
	deviceDesc.ModelName = model.Model
	deviceDesc.GwdModelName = model.Model
	deviceDesc.Scanproto = proto
	deviceDesc.Timestamp = strconv.FormatInt(time.Now().Unix(), 10)
	deviceDesc.IPAddress = model.IPAddress
	deviceDesc.Netmask = model.Netmask
	deviceDesc.Gateway = model.Gateway
	deviceDesc.Hostname = model.Hostname
	deviceDesc.Kernel = model.Kernel
	deviceDesc.Ap = model.Ap
	deviceDesc.ScannedBy = model.ScannedBy
	deviceDesc.ArpMissed = 0
	deviceDesc.IsDHCP = model.IsDHCP
	deviceDesc.IsOnline = true
	deviceDesc.TopologyProto = "snmp"
	deviceDesc.SvcDiscoVia = "udp"
	deviceDesc.Capabilities = *AddCapabilities(deviceDesc)
	deviceDesc.SnmpSupported = "0"
	deviceDesc.SnmpEnabled = "0"
	deviceDesc.Supported = []string{"reboot", "beep", "snmp", "snmpTrap", "syslog", "network", "firmware"}
	InsertAndPublishDevice(deviceDesc)
}

func InsertAndPublishDevice(deviceDesc DevInfo) {
	if InsertDev(deviceDesc) {
		devinfo := make(map[string]DevInfo)
		devinfo[deviceDesc.Mac] = deviceDesc
		err := PublishDevices(&devinfo)
		if err != nil {
			q.Q(err)
		}
	}
}

func FindDevWithIP(ip string) (*DevInfo, error) {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()
	for _, dev := range QC.DevData {
		if dev.IPAddress == ip {
			return &dev, nil
		}
	}
	return nil, fmt.Errorf("no such device %s", ip)
}

func FindDev(Id string) (*DevInfo, error) {
	QC.DevMutex.Lock()
	dev, ok := QC.DevData[Id]
	QC.DevMutex.Unlock()
	if !ok {
		q.Q("device not found", Id)
		return nil, fmt.Errorf("no such device %s", Id)
	}
	q.Q(dev)
	return &dev, nil
}

func FindMacWithCmd(cmd []string) (string, error) {
	found := 0
	standardMac := ""
	for _, v := range cmd {
		tmpMac, err := IsValidMACAddress(v)
		if err == nil {
			standardMac = tmpMac
			q.Q(standardMac)
			found = 1
			break
		}
	}
	if found == 0 {
		q.Q("device not found")
		return "", fmt.Errorf("no such device")
	}
	return standardMac, nil
}

func LockDev(Id string) {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()
	dev, ok := QC.DevData[Id]

	if ok {
		dev.Lock = true
		QC.DevData[Id] = dev
	}
}

func unLockDev(Id string) {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()
	dev, ok := QC.DevData[Id]
	if ok {
		dev.Lock = false
		QC.DevData[Id] = dev
	}
}

func CheckDeviceLock(Id string) error {
	b, err := DevIsLocked(Id)
	if err != nil {
		return err
	}
	if b {
		return fmt.Errorf("%v", "device is updating")
	}
	return nil
}

func DevIsLocked(Id string) (bool, error) {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()
	dev, ok := QC.DevData[Id]
	if ok {
		return dev.Lock, nil
	} else {
		return false, fmt.Errorf("no such device %s", Id)
	}
}

func InsertDev(deviceDesc DevInfo) bool {
	lastTimestamp = strconv.FormatInt(time.Now().Unix(), 10)
	deviceDesc.Timestamp = lastTimestamp
	deviceDesc.Group = GM.GetDeviceSubnetName(deviceDesc.Mac)
	QC.DevMutex.Lock()
	dev, ok := QC.DevData[deviceDesc.Mac]
	QC.DevMutex.Unlock()
	if ok { // update existing entry

		if deviceDesc.Scanproto == "snmp" {
			q.Q("do not override with snmp data")
			return false
		}

		if dev.Scanproto == "agent" && deviceDesc.Scanproto == "gwd" {
			q.Q("do not override with if already scanproto agent")
			return false
		}
		if deviceDesc.Scanproto == "arp" {
			QC.DevMutex.Lock()
			QC.DevData[deviceDesc.Mac] = deviceDesc
			QC.DevMutex.Unlock()

			q.Q("override previous arp entry", dev, deviceDesc, len(QC.DevData))
			return true
		}
		// don't override with incomplete info
		if deviceDesc.ModelName != "" &&
			deviceDesc.Ap != "" &&
			deviceDesc.Kernel != "" &&
			deviceDesc.IPAddress != "" &&
			deviceDesc.Netmask != "" &&
			deviceDesc.Gateway != "" {
			// XXX deviceDesc.Hostname check not done here
			//   to allow for empty hostname on some devices

			QC.DevMutex.Lock()
			QC.DevData[deviceDesc.Mac] = deviceDesc
			QC.DevMutex.Unlock()

			q.Q("override previous entry", dev, deviceDesc, len(QC.DevData))
			return true
		}
		q.Q("incomplete device seen before", dev, deviceDesc, len(QC.DevData))
		return false
	}
	// new device discovered
	q.Q("new device", deviceDesc, len(QC.DevData))
	err := SendSyslog(LOG_INFO, "InsertDev", "new device: "+deviceDesc.Mac)
	if err != nil {
		q.Q(err)
	}
	QC.DevMutex.Lock()
	QC.DevData[deviceDesc.Mac] = deviceDesc
	QC.DevMutex.Unlock()
	if QC.IsRoot {
		// root check devices license
		CheckQCLicense()
	}
	if len(QC.RootURL) > 0 {
		devinfo := make(map[string]DevInfo)
		devinfo[deviceDesc.Mac] = deviceDesc
		err = PublishDevices(&devinfo)
		if err != nil {
			q.Q(err)
		}
	}

	return true
}

func SaveDevices() (string, error) {
	// generate a file with timestamp
	fn := fmt.Sprintf("devices-%s.json", time.Now().Format("20060102T150405"))

	QC.DevMutex.Lock()
	jsonBytes, err := json.Marshal(QC.DevData)
	QC.DevMutex.Unlock()

	if err != nil {
		q.Q(err)
		return "", err
	}
	err = os.WriteFile(fn, jsonBytes, 0o644)
	if err != nil {
		q.Q(err)
		return "", err
	}
	return fn, nil
}

func PublishDevices(devdata *map[string]DevInfo) error {
	// send all devices info to root
	if QC.RootURL == "" /* || !QC.IsRoot */ {
		return fmt.Errorf("skip publishing devices, no root")
	}

	QC.DevMutex.Lock()
	jsonBytes, err := json.Marshal(devdata)
	QC.DevMutex.Unlock()

	if err != nil {
		q.Q(err)
		return err
	}
	q.Q("publishing", string(jsonBytes))

	url := QC.RootURL + "/api/v1/devices"
	q.Q(url)

	// resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonBytes))
	resp, err := PostWithToken(url, QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q(err, QC.RootURL)
	}
	if resp != nil {
		// save close, in resp != nil block
		defer resp.Body.Close()
		if resp.StatusCode != 200 {
			return fmt.Errorf("error: publishing to devices, response status code %v", resp.StatusCode)
		}
		res := make(map[string]interface{})
		err := json.NewDecoder(resp.Body).Decode(&res)
		if err != nil {
			return err
		}
		q.Q(res)
	}

	return nil
}

func AddCapabilities(capDev DevInfo) *map[string]bool {
	Capablities := make(map[string]bool)
	QC.DevMutex.Lock()
	dev, ok := QC.DevData[capDev.Mac]
	QC.DevMutex.Unlock()
	if ok {
		_, exist := dev.Capabilities[capDev.Scanproto]
		if !exist {
			Capablities[capDev.Scanproto] = true
		}
		for k, v := range dev.Capabilities {
			Capablities[k] = v
		}
	} else {
		Capablities[capDev.Scanproto] = true
	}
	return &Capablities
}

func AddDevError(devId string, message string) {
	var devError []string
	// checking device exist or not, because some command not related to device and device id not exist
	QC.DevMutex.Lock()
	dev, ok := QC.DevData[devId]
	QC.DevMutex.Unlock()
	if ok {
		timestamp := time.Now().Format(time.Stamp) // XXX not RFC3339
		var name string
		if len(QC.Name) == 0 {
			name, _ = os.Hostname()
		} else {
			name = QC.Name
		}
		syslogmsg := fmt.Sprintf("<%d>%s %s %s: %s", LOG_ALERT, timestamp, name, "DevErr", message)
		devError = append(devError, syslogmsg)
		devError = append(devError, dev.DeviceErrors...)
		dev.DeviceErrors = devError
		q.Q("device error", dev, len(QC.DevData))
		err := SendSyslog(LOG_ALERT, "DevErr", message)
		if err != nil {
			q.Q(err)
		}
		InsertAndPublishDevice(dev)
	}
}

func IsValidMACAddress(mac string) (string, error) {
	// Standard MAC addresses are 17 characters long with delimiters like ':' or '-'
	if len(mac) != 17 {
		return "", errors.New("invalid MAC address length")
	}
	// Parse the MAC address using net.ParseMAC
	parsedMAC, err := net.ParseMAC(mac)
	if err != nil {
		// Return an error if the MAC address is invalid
		return "", errors.New("invalid MAC Address")
	}
	// Convert to uppercase and use "-" as the delimiter
	formattedMAC := strings.ToUpper(parsedMAC.String())
	formattedMAC = strings.ReplaceAll(formattedMAC, ":", "-")

	return formattedMAC, nil
}

func GetCredentials(dev *DevInfo) (string, string) {
	username := defaultUsername
	password := defaultPassword
	if len(dev.UserName) > 0 {
		username = dev.UserName
	}
	if len(dev.PassWord) > 0 {
		replacedPassWord, err := ExpandCommandKVValue(dev.PassWord)
		if err != nil {
			q.Q(err)
			password = dev.PassWord
		} else {
			password = replacedPassWord
		}
	}
	return username, password
}
