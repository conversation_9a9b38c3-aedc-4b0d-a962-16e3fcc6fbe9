import { ProDescriptions, ProTable } from "@ant-design/pro-components";
import {
  <PERSON><PERSON>,
  Ava<PERSON>,
  Button,
  Card,
  Col,
  Descriptions,
  Divider,
  Flex,
  Row,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { EditOutlined } from "@ant-design/icons";
import { TopologyImage } from "../topology/TopologyImage";
import { useDispatch } from "react-redux";
import { setDeviceDataToEdit } from "../../features/inventory/inventorySlice";
import { render } from "react-dom";

export const deviceColumns = (token) => {
  const { modal } = App.useApp();
  const dispatch = useDispatch();

  return [
    {
      title: "IP Address",
      dataIndex: "ipaddress",
      key: "ipaddress",
      width: 120,
      fixed: "left",
      align: "center",
      render: (data, record) => (
        <Button
          type="link"
          style={{
            color:
              record.type === "IP discovered"
                ? token.colorWarning
                : token.colorPrimary,
          }}
          onClick={() => modal.info(devInfomodel(record))}
        >
          {data}
        </Button>
      ),
    },
    {
      title: "Model",
      width: 200,
      dataIndex: "modelname",
      key: "modelname",
      sorter: (a, b) => (a.model > b.model ? 1 : -1),
    },
    {
      title: "Service Name",
      width: 150,
      dataIndex: "scannedby",
      key: "scannedby",
      ellipsis: true,
      sorter: (a, b) => (a.scannedby > b.scannedby ? 1 : -1),
    },
    {
      title: "MAC Address",
      dataIndex: "mac",
      key: "mac",
      width: 180,
    },
    {
      title: "Host Name",
      dataIndex: "hostname",
      key: "hostname",
      width: 150,
      ellipsis: true,
    },
    {
      title: "Netmask",
      dataIndex: "netmask",
      key: "netmask",
      width: 150,
    },
    {
      title: "kernel",
      dataIndex: "kernel",
      key: "kernel",
      width: 100,
    },
    {
      title: "Firmware Ver.",
      dataIndex: "ap",
      key: "ap",
      width: 200,
      ellipsis: true,
    },
    {
      title: "Agent Ver.",
      dataIndex: "agentVersion",
      key: "agentVersion",
      width: 100,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (data, record) => {
        return data === "online" ? (
          <Tag color="success">Online</Tag> //green
        ) : data === "offline" ? (
          <Tag color="error">Offline</Tag> //red
        ) : (
          <Tag color="warning">Unknown</Tag> //yellow
        );
      },
    },
    {
      title: "Group",
      dataIndex: "group",
      key: "group",
      width: 200,
      ellipsis: true,
      sorter: (a, b) => (a.group > b.group ? 1 : -1),
    },
    {
      title: "Timestamp",
      dataIndex: "timestamp",
      key: "timestamp",
      width: 200,
      render: (data, record) => new Date(data * 1000).toLocaleString(),
    },

    {
      title: "Supported",
      dataIndex: "supported",
      key: "supported",
      width: 200,
      hidden: true,
      render: (data, record) => (data === null ? "" : data.toString()),
    },
    {
      title: "Action",
      key: "operation",
      width: 100,
      fixed: "right",
      align: "center",
      render: (data, record) =>
        record.type === "IP discovered" ? (
          <Tooltip title="edit manual device">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => dispatch(setDeviceDataToEdit(record))}
            />
          </Tooltip>
        ) : null,
    },
  ];
};

const devInfomodel = (record) => {
  return {
    icon: null,
    width: record.type === "motor-ctrl-card" ? 850 : 600,
    title:
      record.type === "motor-ctrl-card" ? "MDR Device info" : "Device info",
    style: { top: 20 },
    content:
      record.type === "motor-ctrl-card"
        ? mdrContent(record)
        : deviceContent(record),
  };
};

const deviceContent = (record) => (
  <Card
    style={{
      minWidth: 550,
    }}
  >
    <Card.Meta
      avatar={
        <Avatar
          shape="square"
          src={TopologyImage(record.modelname)}
          size="large"
        />
      }
      title={`${record.mac} (${record.hostname})`}
      description={
        <ProDescriptions
          dataSource={record}
          column={2}
          columns={[
            {
              title: "Model",
              key: "modelname",
              dataIndex: "modelname",
              span: 2,
            },
            {
              title: "Ip Address",
              key: "ipaddress",
              dataIndex: "ipaddress",
            },
            {
              title: "Netmask",
              key: "netmask",
              dataIndex: "netmask",
            },
            {
              title: "Gateway",
              key: "gateway",
              dataIndex: "gateway",
            },
            {
              title: "DHCP",
              key: "isdhcp",
              dataIndex: "isdhcp",
              render: (data) => {
                return data ? "YES" : "NO";
              },
            },
            {
              title: "F/W Version",
              key: "ap",
              dataIndex: "ap",
              span: 2,
            },
            {
              title: "Kernel",
              key: "kernel",
              dataIndex: "kernel",
            },
            {
              title: "Agent Version",
              key: "agentVersion",
              dataIndex: "agentVersion",
            },
            {
              title: "Agent",
              key: "capabilities.agent",
              dataIndex: "capabilities",
              render: (data) => {
                return data?.agent ? "YES" : "NO";
              },
            },
            {
              title: "GWD",
              key: "capabilities.gwd",
              dataIndex: "capabilities",
              render: (data) => {
                return data?.gwd ? "YES" : "NO";
              },
            },
            {
              title: "SNMP support",
              key: "snmpSupported",
              dataIndex: "snmpSupported",
              render: (data) => {
                return data === "1" ? "YES" : "NO";
              },
            },
            {
              title: "SNMP Enabled",
              key: "snmpEnabled",
              dataIndex: "snmpEnabled",
              render: (data) => {
                return data === "1" ? "YES" : "NO";
              },
            },
            {
              title: "Read Community",
              key: "readcommunity",
              dataIndex: "readcommunity",
            },
            {
              title: "Write Community",
              key: "writecommunity",
              dataIndex: "writecommunity",
            },
            {
              title: "Scan proto",
              key: "scanproto",
              dataIndex: "scanproto",
            },
            {
              title: "Topology proto",
              key: "topologyproto",
              dataIndex: "topologyproto",
            },
            {
              title: "Service Name",
              key: "scannedby",
              dataIndex: "scannedby",
            },
            {
              title: "Tunnel URL",
              key: "tunneled_url",
              dataIndex: "tunneled_url",
              ellipsis: true,
            },
            {
              title: "Timestamp",
              key: "timestamp",
              dataIndex: "timestamp",
              span: 2,
              render: (data) => new Date(data * 1000).toLocaleString(),
            },
            {
              title: undefined,
              key: "supported",
              dataIndex: "supported",
              span: 2,
              render: (data) => (
                <Flex vertical gap={10}>
                  <span className="ant-descriptions-item-label">Supported</span>
                  <Row gutter={[8, 8]}>
                    {data &&
                      data.map((item) => (
                        <Col span={8} key={item}>
                          <Tag color="gold">{item}</Tag>
                        </Col>
                      ))}
                  </Row>
                </Flex>
              ),
            },
          ]}
        />
      }
    />
  </Card>
);

const mdrContent = (record) => (
  <Card
    style={{
      minWidth: 800,
    }}
  >
    <Card.Meta
      avatar={
        <Avatar
          shape="square"
          src={TopologyImage(record.modelname)}
          size="large"
        />
      }
      title={`${record.mac} (${record.hostname})`}
      description={
        <ProDescriptions
          dataSource={record}
          column={3}
          columns={[
            {
              title: "Model",
              key: "modelname",
              dataIndex: "modelname",
              span: 2,
            },
            {
              title: "Service Name",
              key: "scannedby",
              dataIndex: "scannedby",
            },
            {
              title: "Ip Address",
              key: "ipaddress",
              dataIndex: "ipaddress",
            },
            {
              title: "Netmask",
              key: "netmask",
              dataIndex: "netmask",
            },
            {
              title: "Gateway",
              key: "gateway",
              dataIndex: "gateway",
            },
            {
              title: "DHCP",
              key: "isdhcp",
              dataIndex: "isdhcp",
              render: (data) => {
                return data ? "YES" : "NO";
              },
            },
            {
              title: "F/W Version",
              key: "ap",
              dataIndex: "ap",
              span: 2,
            },
            {
              title: "Kernel",
              key: "kernel",
              dataIndex: "kernel",
            },
            {
              title: "Timestamp",
              key: "timestamp",
              dataIndex: "timestamp",
              span: 2,
              render: (data) => new Date(data * 1000).toLocaleString(),
            },
            {
              title: "",
              key: "motorinfo",
              dataIndex: "motorinfo",
              span: 3,
              render: (data) => (
                <>
                  <Divider
                    style={{ margin: 0 }}
                  >{`Motor Info (${data.sn})`}</Divider>
                </>
              ),
            },
            {
              title: "",
              key: "ethernet",
              dataIndex: "motorinfo",
              span: 3,
              render: (data) =>
                data.ethernet && (
                  <Descriptions
                    title="Ethernet"
                    items={data.ethernet?.map((item) => ({
                      key: item.port,
                      label: `port ${item.port}`,
                      children: item.link,
                    }))}
                    column={3}
                  />
                ),
            },
            {
              title: "",
              key: "profinet",
              dataIndex: "motorinfo",
              span: 3,
              render: (data) => (
                <ProDescriptions
                  title="Profinet"
                  dataSource={data.profinet}
                  column={3}
                  columns={[
                    {
                      title: "NodeName",
                      key: "nodeName",
                      dataIndex: "nodeName",
                    },
                    {
                      title: "ModuleInId",
                      key: "moduleInId",
                      dataIndex: "moduleInId",
                    },
                    {
                      title: "ModuleOutId",
                      key: "moduleOutId",
                      dataIndex: "moduleOutId",
                    },
                  ]}
                />
              ),
            },
            {
              title: "",
              key: "mdr",
              dataIndex: "motorinfo",
              span: 3,
              render: (data) => (
                <ProTable
                  cardProps={{ bodyStyle: { padding: 0 } }}
                  dataSource={data.mdr}
                  headerTitle={
                    <Typography.Title level={5}>MDR details</Typography.Title>
                  }
                  rowKey="zone"
                  options={false}
                  search={false}
                  pagination={false}
                  columns={[
                    { key: "zone", dataIndex: "zone", title: "Zone" },
                    { key: "sn", dataIndex: "sn", title: "sn" },
                    {
                      key: "sensor-type",
                      dataIndex: "sensorType",
                      title: "sensor-type",
                    },
                    {
                      key: "sensor-status",
                      dataIndex: "sensorStatus",
                      title: "sensor-status",
                    },
                    {
                      key: "zone-status",
                      dataIndex: "zoneStatus",
                      title: "zone-status",
                    },
                    {
                      title: "Direction",
                      key: "direction",
                      dataIndex: "direction",
                    },
                    {
                      title: "Level",
                      key: "level",
                      dataIndex: "level",
                    },
                  ]}
                  expandable={{
                    expandedRowRender: (record) => (
                      <ProDescriptions
                        dataSource={record}
                        column={3}
                        columns={[
                          {
                            title: "Drive Temperature",
                            key: "driveTemperature",
                            dataIndex: "driveTemperature",
                            renderText: (data) => `${data} ℃`,
                          },
                          {
                            title: "Drive Current",
                            key: "driveCurrent",
                            dataIndex: "driveCurrent",
                            renderText: (data) => `${data} Amp.`,
                          },
                          {
                            title: "Voltage",
                            key: "voltage",
                            dataIndex: "voltage",
                            renderText: (data) => `${data} V`,
                          },
                          {
                            title: "Speed Code",
                            key: "speedCode",
                            dataIndex: "speedCode",
                          },
                          {
                            title: "Running Time",
                            key: "runningTime",
                            dataIndex: "runningTime",
                            renderText: (data) => `${data} min.`,
                          },
                          {
                            title: "Temperature",
                            key: "temperature",
                            dataIndex: "temperature",
                            renderText: (data) => `${data} ℃`,
                          },
                          {
                            title: "Mode",
                            key: "mode",
                            dataIndex: "mode",
                          },
                          {
                            title: "Holding",
                            key: "holding",
                            dataIndex: "holding",
                          },
                          {
                            title: "Speed",
                            key: "speed",
                            dataIndex: "speed",
                            renderText: (data) => `${data} mm/sec.`,
                          },
                        ]}
                      />
                    ),
                  }}
                />
              ),
            },
          ]}
        />
      }
    />
  </Card>
);
