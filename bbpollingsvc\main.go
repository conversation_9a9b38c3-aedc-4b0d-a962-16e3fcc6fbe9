package main

import (
	"flag"
	"fmt"
	"os"
	"runtime/debug"
	"sync"

	"mnms"

	"github.com/qeof/q"
)

var Version string

func printVersion() {
	if Version == "" {
		Version = "Development version"
	}
	// print out version
	fmt.Fprintf(os.<PERSON>derr, "BlackBear NIMBL Version: %s\n", Version)
	info, _ := debug.ReadBuildInfo()
	fmt.Fprintln(os.Stderr, "Build information:")
	fmt.Fprintln(os.Stderr, info.GoVersion, info.Main, info.Settings)
}

func main() {
	var wg sync.WaitGroup
	q.O = "stderr"
	q.P = ""

	stop := func() {
		mnms.ClientExit()
		mnms.SyslogExit()
	}

	flagversion := flag.Bool("version", false, "print version")
	flag.IntVar(&mnms.QC.Port, "p", 27189, "port")
	flag.StringVar(&mnms.QC.RootURL, "r", "", "root URL")
	flag.StringVar(&mnms.QC.Name, "n", "", "name")
	flag.StringVar(&q.O, "O", "stderr", "debug log output")
	dp := flag.String("P", "", "debug log pattern string")
	debuglog := flag.Bool("debuglog", false, "enable debug log, this will override -P to .*")
	flag.BoolVar(&mnms.QC.DumpStackTrace, "ds", false, "dump stack trace when exiting with non zero code")
	flag.IntVar(&mnms.QC.RegisterInterval, "ir", mnms.QC.RegisterInterval, "Network service registration interval")
	flag.StringVar(&mnms.QC.SyslogServerAddr, "ss",
		":5554", "syslog server address")
	flag.StringVar(&mnms.QC.RemoteSyslogServerAddr, "rs",
		mnms.QC.RemoteSyslogServerAddr, "remote syslog server address")
	nmsName := flag.String("nsn", "", "Specify the NIMBL network service name for polling")
	pollingInterval := flag.Int("pi", 10, "Specify the polling interval in seconds")

	var daemon string
	flag.StringVar(&daemon, mnms.DaemonFlag, "", mnms.Usage)
	flag.Parse()

	// version
	if Version != "" {
		mnms.QC.Version = Version
	}
	service := func() {
		if *flagversion {
			printVersion()
			mnms.DoExit(0)
		}

		if *debuglog {
			*dp = ".*"
		}

		if *dp == "." {
			fmt.Fprintln(os.Stderr, "error: invalid debug pattern")
			mnms.DoExit(1)
		}
		q.P = *dp
		q.Q(q.O, q.P)

		if mnms.QC.Name == "" {
			fmt.Fprintln(os.Stderr, "error: -n name is required")
			mnms.DoExit(1)
		}
		if mnms.QC.RootURL == "" {
			fmt.Fprintln(os.Stderr, "error: -r root URL is required")
			mnms.DoExit(1)
		}
		if *nmsName == "" {
			fmt.Fprintln(os.Stderr, "error: -nsn nms name is required")
			mnms.DoExit(1)
		}
		if mnms.QC.RemoteSyslogServerAddr == "" {
			fmt.Fprintln(os.Stderr, "error: -rs remote syslog server address is required")
			mnms.DoExit(1)
		}
		q.Q(mnms.QC.Name)
		q.Q(mnms.QC.RootURL)
		q.Q(*nmsName)
		mnms.QC.Kind = "polling"
		var err error
		mnms.QC.AdminToken, err = mnms.GetToken("admin")
		if err != nil {
			q.Q(err)
			fmt.Fprintln(os.Stderr, "error: can't get admin token")
			mnms.DoExit(1)
		}
		// RegisterMain
		registerMain := func() {
			if mnms.QC.RootURL != "" {
				wg.Add(1)
				q.Q(mnms.QC.RegisterInterval)
				go func() {
					defer wg.Done()
					mnms.RegisterMain()
				}()
			}
		}
		runPolling := func() {
			wg.Add(1)
			go func() {
				defer wg.Done()
				mnms.StartSyslogServer()
				q.Q("syslog server returned")
			}()
			wg.Add(1)
			go func() {
				defer wg.Done()
				mnms.StartPolling(*nmsName, *pollingInterval)
			}()
		}
		registerMain()
		runPolling()
		wg.Wait()
		q.Q("exit normally")
		mnms.DoExit(0)
	}
	// enable Daemon
	s, err := mnms.NewDaemon(mnms.QC.Name, os.Args)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
	s.RegisterRunEvent(service)
	s.RegisterStopEvent(stop)
	err = s.RunMode(daemon)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
}
